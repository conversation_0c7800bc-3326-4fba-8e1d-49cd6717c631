{
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "抽奖点餐",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/lottery/lottery",
      "style": {
        "navigationBarTitleText": "抽奖",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white",
        "disableScroll": false,
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/result/result",
      "style": {
        "navigationBarTitleText": "抽奖结果",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/records/records",
      "style": {
        "navigationBarTitleText": "我的记录",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/claim/claim",
      "style": {
        "navigationBarTitleText": "领取奖品",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/webview/webview",
      "style": {
        "navigationBarTitleText": "点餐",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    },
    {
      "path": "pages/test-modal/test-modal",
      "style": {
        "navigationBarTitleText": "弹窗测试",
        "navigationBarBackgroundColor": "#667eea",
        "navigationBarTextStyle": "white"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "white",
    "navigationBarTitleText": "抽奖点餐",
    "navigationBarBackgroundColor": "#667eea",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {}
}
